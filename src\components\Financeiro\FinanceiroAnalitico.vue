<template>
  <div class="financeiro-analitico">
    <!-- Filtros e Busca -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header pb-0">
            <h6 class="mb-0">Filtros de Busca</h6>
          </div>
          <div class="card-body">
            <div class="row g-3">
              <!-- Primeira coluna: Paciente e Status -->
              <div class="col-md-5">
                <div class="row g-3">
                  <div class="col-12">
                    <label class="form-label">Paciente</label>
                    <select class="form-select" v-model="filters.paciente_id" @change="applyFilters">
                      <option value="">Todos os pacientes</option>
                      <option v-for="paciente in pacientes" :key="paciente.id" :value="paciente.id">
                        {{ paciente.nome }}
                      </option>
                    </select>
                  </div>
                  <div class="col-12">
                    <label class="form-label">Status</label>
                    <div class="status-toggles w-100">
                      <button
                        v-for="status in statusOptions"
                        :key="status.value"
                        class="btn status-toggle flex-fill mb-0"
                        :class="getStatusToggleClass(status.value)"
                        @click="toggleStatus(status.value)"
                        :title="status.label"
                      >
                        <i :class="status.icon" class="me-1"></i>
                        {{ status.label }}
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Segunda coluna: Datas -->
              <div class="col-md-4">
                <div class="row g-3">
                  <div class="col-12">
                    <label class="form-label">Data Início</label>
                    <input type="date" class="form-control" v-model="filters.data_inicio" @change="applyFilters">
                  </div>
                  <div class="col-12">
                    <label class="form-label">Data Fim</label>
                    <input type="date" class="form-control" v-model="filters.data_fim" @change="applyFilters">
                  </div>
                </div>
              </div>

              <!-- Terceira coluna: Botão de busca -->
              <div class="col-md-3 d-flex align-items-end">
                <button class="btn btn-primary w-100" @click="applyFilters">
                  <i class="fas fa-search me-1"></i>
                  Buscar
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Estatísticas Rápidas -->
    <div class="row mb-4" v-if="estatisticas">
      <div class="col-md-3">
        <div class="stats-card">
          <div class="stats-title">
            <i class="fas fa-check-circle me-1"></i>
            Recebido ({{ estatisticas.quantidade_paga || 0 }})
          </div>
          <div class="stats-value text-success">{{ formatCurrency(estatisticas.total_pago) }}</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stats-card">
          <div class="stats-title">
            <i class="fas fa-clock me-1"></i>
            Pendente ({{ estatisticas.quantidade_pendente || 0 }})
          </div>
          <div class="stats-value text-warning">{{ formatCurrency(estatisticas.total_pendente) }}</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stats-card">
          <div class="stats-title">
            <i class="fas fa-exclamation-triangle me-1"></i>
            Vencido ({{ estatisticas.quantidade_vencida || 0 }})
          </div>
          <div class="stats-value text-danger">{{ formatCurrency(estatisticas.total_vencido) }}</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stats-card">
          <div class="stats-title">
            <i class="fas fa-calculator me-1"></i>
            Total Geral
          </div>
          <div class="stats-value text-info">{{ formatCurrency(estatisticas.total_geral) }}</div>
        </div>
      </div>
    </div>

    <!-- Tabela de Faturas -->
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header pb-0">
            <h6 class="mb-0">Faturas</h6>
          </div>
          <div class="card-body px-0 pt-0 pb-2">
            <div class="table-responsive p-0">
              <table class="table align-items-center mb-0">
                <thead>
                  <tr>
                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Paciente
                    </th>
                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">
                      Descrição
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Valor
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Vencimento
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Status
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-if="loading">
                    <td colspan="6" class="text-center py-4">
                      <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Carregando...</span>
                      </div>
                    </td>
                  </tr>
                  <tr v-else-if="faturas.length === 0">
                    <td colspan="6" class="text-center py-4 text-muted">
                      Nenhuma fatura encontrada
                    </td>
                  </tr>
                  <tr v-else v-for="fatura in faturas" :key="fatura.id">
                    <td>
                      <div class="d-flex px-2 py-1">
                        <div class="d-flex flex-column justify-content-center">
                          <h6 class="mb-0 text-sm">{{ fatura.paciente?.nome || 'Paciente não informado' }}</h6>
                          <p class="text-xs text-secondary mb-0">
                            ID: {{ fatura.paciente?.id || '-' }}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td>
                      <p class="text-xs font-weight-bold mb-0">{{ fatura.descricao }}</p>
                      <p class="text-xs text-secondary mb-0" v-if="fatura.observacoes">
                        {{ fatura.observacoes }}
                      </p>
                    </td>
                    <td class="align-middle text-center text-sm">
                      <span class="font-weight-bold">{{ formatCurrency(fatura.valor_final) }}</span>
                      <div v-if="fatura.parcelas_total > 1" class="text-xs text-secondary">
                        {{ fatura.parcela_numero }}/{{ fatura.parcelas_total }}
                      </div>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">
                        {{ formatDate(fatura.data_vencimento) }}
                      </span>
                    </td>
                    <td class="align-middle text-center text-sm">
                      <span class="badge badge-sm" :class="getStatusBadgeClass(fatura.status)">
                        {{ getStatusText(fatura.status) }}
                      </span>
                    </td>
                    <td class="align-middle text-center">
                      <div class="dropdown">
                        <button class="btn btn-link text-secondary mb-0" data-bs-toggle="dropdown">
                          <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                          <li>
                            <a class="dropdown-item" href="#" @click.prevent="$emit('edit', fatura)">
                              <i class="fas fa-edit me-2"></i>
                              Editar
                            </a>
                          </li>
                          <li v-if="fatura.status === 'pendente'">
                            <a class="dropdown-item" href="#" @click.prevent="markAsPaid(fatura)">
                              <i class="fas fa-check me-2"></i>
                              Marcar como Pago
                            </a>
                          </li>
                          <li>
                            <a class="dropdown-item text-danger" href="#" @click.prevent="$emit('delete', fatura.id)">
                              <i class="fas fa-trash me-2"></i>
                              Cancelar
                            </a>
                          </li>
                        </ul>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { financeiroService } from '@/services/financeiroService';

export default {
  name: 'FinanceiroAnalitico',
  props: {
    faturas: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    estatisticas: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      pacientes: [],
      filters: {
        paciente_id: '',
        statusList: [], // Array de status ativos
        data_inicio: '',
        data_fim: ''
      },
      statusOptions: [
        { value: 'pendente', label: 'Pendente', icon: 'fas fa-clock' },
        { value: 'pago', label: 'Pago', icon: 'fas fa-check-circle' },
        { value: 'vencido', label: 'Vencido', icon: 'fas fa-exclamation-triangle' },
        { value: 'cancelado', label: 'Cancelado', icon: 'fas fa-times-circle' }
      ]
    };
  },
  methods: {
    formatCurrency: financeiroService.formatCurrency,
    formatDate: financeiroService.formatDate,
    getStatusBadgeClass: financeiroService.getStatusBadgeClass,
    getStatusText: financeiroService.getStatusText,

    applyFilters() {
      // Converter statusList para o formato esperado pelo backend
      const filtersToSend = {
        ...this.filters,
        status: this.filters.statusList.length > 0 ? this.filters.statusList : ''
      };
      this.$emit('refresh', filtersToSend);
    },

    toggleStatus(status) {
      const index = this.filters.statusList.indexOf(status);
      if (index > -1) {
        // Remove o status se já estiver selecionado
        this.filters.statusList.splice(index, 1);
      } else {
        // Adiciona o status se não estiver selecionado
        this.filters.statusList.push(status);
      }
      this.applyFilters();
    },

    getStatusToggleClass(status) {
      const isActive = this.filters.statusList.includes(status);
      const baseClass = 'btn-outline-';
      const activeClass = 'btn-';

      switch (status) {
        case 'pendente':
          return isActive ? activeClass + 'warning' : baseClass + 'warning';
        case 'pago':
          return isActive ? activeClass + 'success' : baseClass + 'success';
        case 'vencido':
          return isActive ? activeClass + 'danger' : baseClass + 'danger';
        case 'cancelado':
          return isActive ? activeClass + 'secondary' : baseClass + 'secondary';
        default:
          return baseClass + 'primary';
      }
    },

    markAsPaid(fatura) {
      const paymentData = {
        data_pagamento: new Date().toISOString().split('T')[0],
        meio_pagamento: 'Não especificado'
      };
      this.$emit('mark-paid', fatura.id, paymentData);
    },

    async loadPacientes() {
      try {
        // TODO: Implementar carregamento de pacientes
        // const response = await pacientesService.getPacientes();
        // this.pacientes = response.data.data;
      } catch (error) {
        console.error('Erro ao carregar pacientes:', error);
      }
    }
  },

  mounted() {
    this.loadPacientes();
  }
};
</script>

<style scoped>
.financeiro-analitico .card {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.badge-warning {
  background-color: #fb6340;
}

/* Estilos para os cards de estatísticas */
.stats-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 0.8rem;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.stats-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: #344767;
  text-transform: uppercase;
  margin-bottom: 0.4rem;
  letter-spacing: 0.5px;
}

.stats-value {
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.badge-success {
  background-color: #2dce89;
}

.badge-danger {
  background-color: #f5365c;
}

.badge-secondary {
  background-color: #8898aa;
}

/* Estilos para os toggles de status */
.status-toggles {
  display: flex;
  gap: 0;
  border-radius: 0.375rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.status-toggle {
  border-radius: 0 !important;
  border-right: none !important;
  font-size: 0.75rem;
  padding: 0.4rem 0.5rem;
  font-weight: 600;
  transition: all 0.2s ease;
  white-space: nowrap;
  flex: 1;
  text-align: center;
}

.status-toggle:last-child {
  border-right: 1px solid !important;
}

.status-toggle:first-child {
  border-top-left-radius: 0.375rem !important;
  border-bottom-left-radius: 0.375rem !important;
}

.status-toggle:last-child {
  border-top-right-radius: 0.375rem !important;
  border-bottom-right-radius: 0.375rem !important;
}

.status-toggle:hover {
  transform: translateY(-1px);
  z-index: 2;
  position: relative;
}
</style>

<template>
  <lumi-sidenav
    icon="mdi-currency-usd"
    class="fixed-end lumi-sidenav"
    v-if="showSidenav"
    :config="sidenavConfig"
    @action="handleSidenavAction"
  ></lumi-sidenav>

  <div class="main-page-content">
    <div class="py-4 container-fluid">
      <!-- Navegação por abas -->
      <div class="row mb-2">
        <div class="col-12">
          <div class="elegant-nav-wrapper">
            <div class="elegant-nav-container">
              <button
                class="elegant-nav-item"
                :class="{ active: activeTab === 'analitico' }"
                @click="activeTab = 'analitico'"
              >
                <i class="fas fa-list me-2"></i>
                <span>Gestão Detalhada</span>
              </button>
              <button
                class="elegant-nav-item"
                :class="{ active: activeTab === 'sintetico' }"
                @click="activeTab = 'sintetico'"
              >
                <i class="fas fa-chart-pie me-2"></i>
                <span>Dashboard Executivo</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Conte<PERSON>do das abas -->
      <div class="tab-content" id="tabs-tabContent">
        <!-- Aba Gestão Detalhada (Analítico) -->
        <div class="tab-pane fade"
             :class="{ 'show active': activeTab === 'analitico' }"
             id="analitico"
             role="tabpanel">
          <financeiro-analitico
            :faturas="faturas"
            :loading="loading.faturas"
            :estatisticas="estatisticas"
            @refresh="loadFaturas"
            @create="openCreateCompleteModal"
            @edit="openEditModal"
            @delete="deleteFatura"
            @mark-paid="markAsPaid"
            @generate-receipt="generateReceipt"
            @duplicate="duplicateFatura"
            @send-reminder="sendReminder"
          />
        </div>

        <!-- Aba Dashboard Executivo (Sintético) -->
        <div class="tab-pane fade"
             :class="{ 'show active': activeTab === 'sintetico' }"
             id="sintetico"
             role="tabpanel">
          <financeiro-sintetico
            :estatisticas="estatisticas"
            :faturas="faturas"
            :loading="loading.estatisticas"
            @refresh="loadEstatisticas"
          />
        </div>
      </div>
    </div>
  </div>

  <!-- Modal para criar/editar fatura -->
  <financeiro-modal
    ref="financeiroModal"
    @saved="onFaturaSaved"
  />

  <!-- Modal de Criação Completo -->
  <financeiro-create-modal
    ref="financeiroCreateModal"
    @saved="onFaturaSaved"
  />
</template>
<script>
import { mapState, mapMutations } from "vuex";
import LumiSidenav from "@/views/components/LumiSidenav/index.vue";
import FinanceiroAnalitico from "@/components/Financeiro/FinanceiroAnalitico.vue";
import FinanceiroSintetico from "@/components/Financeiro/FinanceiroSintetico.vue";
import FinanceiroModal from "@/components/Financeiro/FinanceiroModal.vue";
import FinanceiroCreateModal from "@/components/Financeiro/FinanceiroCreateModal.vue";
import { financeiroService } from "@/services/financeiroService";
import cSwal from "@/utils/cSwal.js";

export default {
  name: "Financeiro",
  components: {
    LumiSidenav,
    FinanceiroAnalitico,
    FinanceiroSintetico,
    FinanceiroModal,
    FinanceiroCreateModal,
  },
  data() {
    return {
      activeTab: 'analitico',
      faturas: [],
      estatisticas: {},
      loading: {
        faturas: false,
        estatisticas: false,
      },
      sidenavConfig: {
        groups: [
          {
            title: "FINANCEIRO",
            buttons: [
              {
                text: "Nova Fatura",
                icon: "add",
                iconType: "material",
                action: "newFatura",
                attributes: {
                  "data-bs-toggle": "modal",
                  "data-bs-target": "#modalFinanceiro"
                }
              },
              {
                text: "Relatório Mensal",
                icon: "assessment",
                iconType: "material",
                action: "monthlyReport"
              },
              {
                text: "Exportar Dados",
                icon: "download",
                iconType: "material",
                action: "exportData"
              }
            ]
          }
        ]
      }
    };
  },
  computed: {
    ...mapState(["showSidenav"]),
  },
  methods: {
    ...mapMutations(["navbarMinimize"]),

    handleSidenavAction(action, button) {
      console.log(`Action: ${action}`, button);

      switch (action) {
        case 'newFatura':
          this.openCreateModal();
          break;
        case 'monthlyReport':
          this.generateMonthlyReport();
          break;
        case 'exportData':
          this.exportData();
          break;
      }
    },

    async loadFaturas() {
      this.loading.faturas = true;
      try {
        const response = await financeiroService.getFaturas();
        this.faturas = response.data.data.data || [];
      } catch (error) {
        console.error('Erro ao carregar faturas:', error);
        cSwal.cError('Erro ao carregar faturas');
      } finally {
        this.loading.faturas = false;
      }
    },

    async loadEstatisticas() {
      this.loading.estatisticas = true;
      try {
        const response = await financeiroService.getEstatisticas();
        this.estatisticas = response.data.data;
      } catch (error) {
        console.error('Erro ao carregar estatísticas:', error);
        cSwal.cError('Erro ao carregar estatísticas');
      } finally {
        this.loading.estatisticas = false;
      }
    },

    openCreateModal() {
      this.$refs.financeiroModal.openCreate();
    },

    openCreateCompleteModal() {
      this.$refs.financeiroCreateModal.open('fatura');
    },

    openEditModal(fatura) {
      this.$refs.financeiroModal.openEdit(fatura);
    },

    async deleteFatura(faturaId) {
      if (confirm('Tem certeza que deseja cancelar esta fatura?')) {
        try {
          await financeiroService.deleteFatura(faturaId);
          cSwal.cSuccess('Fatura cancelada com sucesso');
          this.loadFaturas();
        } catch (error) {
          console.error('Erro ao cancelar fatura:', error);
          cSwal.cError('Erro ao cancelar fatura');
        }
      }
    },

    async markAsPaid(faturaId, paymentData) {
      try {
        await financeiroService.markAsPaid(faturaId, paymentData);
        cSwal.cSuccess('Fatura marcada como paga');
        this.loadFaturas();
        this.loadEstatisticas();
      } catch (error) {
        console.error('Erro ao marcar fatura como paga:', error);
        cSwal.cError('Erro ao marcar fatura como paga');
      }
    },

    onFaturaSaved() {
      this.loadFaturas();
      this.loadEstatisticas();
    },

    generateMonthlyReport() {
      // TODO: Implementar geração de relatório mensal
      cSwal.cInfo('Funcionalidade em desenvolvimento');
    },

    exportData() {
      // TODO: Implementar exportação de dados
      cSwal.cInfo('Funcionalidade em desenvolvimento');
    },

    generateReceipt(fatura) {
      // TODO: Implementar geração de recibo
      cSwal.cInfo(`Gerando recibo para a fatura: ${fatura.descricao}`);
    },

    duplicateFatura(fatura) {
      // TODO: Implementar duplicação de fatura
      cSwal.cInfo(`Duplicando fatura: ${fatura.descricao}`);
    },

    sendReminder(fatura) {
      // TODO: Implementar envio de lembrete
      cSwal.cSuccess(`Lembrete enviado para ${fatura.paciente?.nome || 'paciente'}`);
    },
  },

  async mounted() {
    await Promise.all([
      this.loadFaturas(),
      this.loadEstatisticas()
    ]);
  },
};
</script>

<style scoped>
/* Navbar Elegante */
.elegant-nav-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 0.5rem;
}

.elegant-nav-container {
  display: inline-flex;
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  border-radius: 10px;
  padding: 3px;
  box-shadow: 0 3px 12px rgba(66, 153, 225, 0.25);
  position: relative;
  overflow: hidden;
}

.elegant-nav-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0.03) 100%);
  pointer-events: none;
}

.elegant-nav-item {
  position: relative;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.85);
  padding: 0.6rem 1.25rem;
  border-radius: 7px;
  font-weight: 600;
  font-size: 0.85rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  display: flex;
  align-items: center;
  white-space: nowrap;
  z-index: 2;
}

.elegant-nav-item:hover {
  color: rgba(255, 255, 255, 0.95);
  background: rgba(255, 255, 255, 0.12);
  transform: translateY(-1px);
}

.elegant-nav-item.active {
  background: rgba(255, 255, 255, 0.95);
  color: #3182ce;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
}

.elegant-nav-item.active:hover {
  background: rgba(255, 255, 255, 1);
  color: #2c5aa0;
}

.elegant-nav-item i {
  transition: transform 0.3s ease;
}

.elegant-nav-item:hover i,
.elegant-nav-item.active i {
  transform: scale(1.1);
}

.elegant-nav-item span {
  transition: all 0.3s ease;
}

/* Animação suave para mudança de abas */
.tab-content .tab-pane {
  transition: opacity 0.3s ease-in-out;
}

.tab-content .tab-pane:not(.show) {
  opacity: 0;
}

.tab-content .tab-pane.show {
  opacity: 1;
}
</style>
